/* Footer component specific styles */
.footer {
  background: #1f2937;
  color: white;
  margin-top: auto;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.footerContent {
  padding: 3rem 0 2rem;
}

.footerGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footerSection h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: white;
}

.footerLinks {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.footerLink {
  color: #d1d5db;
  text-decoration: none;
  font-size: 0.875rem;
  transition: color 0.3s ease;
}

.footerLink:hover {
  color: #3b82f6;
}

.contactInfo {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.contactItem {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  color: #d1d5db;
  font-size: 0.875rem;
}

.contactIcon {
  margin-top: 0.125rem;
  flex-shrink: 0;
}

.socialLinks {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.socialLink {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background: #374151;
  border-radius: 50%;
  color: #d1d5db;
  text-decoration: none;
  transition: all 0.3s ease;
}

.socialLink:hover {
  background: #3b82f6;
  color: white;
  transform: translateY(-2px);
}

.footerBottom {
  border-top: 1px solid #374151;
  padding: 1.5rem 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  text-align: center;
}

.bottomLinks {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
  justify-content: center;
}

.bottomLink {
  color: #9ca3af;
  text-decoration: none;
  font-size: 0.875rem;
  transition: color 0.3s ease;
}

.bottomLink:hover {
  color: #3b82f6;
}

.copyright {
  color: #9ca3af;
  font-size: 0.875rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .footerContent {
    padding: 2rem 0 1.5rem;
  }
  
  .footerGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .socialLinks {
    justify-content: center;
  }
  
  .footerBottom {
    text-align: center;
  }
  
  .bottomLinks {
    flex-direction: column;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 0.5rem;
  }
  
  .footerContent {
    padding: 1.5rem 0 1rem;
  }
  
  .socialLinks {
    gap: 0.75rem;
  }
  
  .socialLink {
    width: 2rem;
    height: 2rem;
    font-size: 0.875rem;
  }
}
