/* About page specific styles */
.aboutPage {
  background-color: #f9fafb;
  min-height: 100vh;
}

.pageHeader {
  background: white;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.headerContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.pageTitle {
  font-size: 1.875rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.pageSubtitle {
  color: #4b5563;
}

.heroSection {
  padding: 4rem 0;
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  color: white;
  text-align: center;
}

.heroTitle {
  font-size: 2.25rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
}

.heroDescription {
  font-size: 1.25rem;
  max-width: 48rem;
  margin: 0 auto;
  line-height: 1.625;
}

.missionVision {
  padding: 4rem 0;
}

.mvGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 3rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.mvCard {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  transition: all 0.3s ease;
}

.mvCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.mvIcon {
  color: #2563eb;
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.mvTitle {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #1f2937;
}

.mvDescription {
  color: #4b5563;
  line-height: 1.625;
}

.coreValues {
  padding: 4rem 0;
  background: white;
}

.sectionTitle {
  font-size: 1.875rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 3rem;
  color: #1f2937;
}

.valuesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.valueItem {
  text-align: center;
}

.valueIcon {
  background: #dbeafe;
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
}

.valueIcon span {
  color: #2563eb;
  font-size: 1.5rem;
}

.valueTitle {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #1f2937;
}

.valueDescription {
  color: #4b5563;
  font-size: 0.875rem;
  line-height: 1.5;
}

.timeline {
  padding: 4rem 0;
}

.timelineContainer {
  max-width: 64rem;
  margin: 0 auto;
  padding: 0 1rem;
}

.timelineItem {
  display: flex;
  align-items: flex-start;
  margin-bottom: 2rem;
}

.timelineYear {
  background: #2563eb;
  color: white;
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 0.875rem;
  margin-right: 1.5rem;
  flex-shrink: 0;
}

.timelineContent {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  flex: 1;
}

.timelineTitle {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #1f2937;
}

.timelineDescription {
  color: #4b5563;
  line-height: 1.5;
}

.team {
  padding: 4rem 0;
  background: white;
}

.teamGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.teamMember {
  text-align: center;
}

.memberImage {
  width: 8rem;
  height: 8rem;
  border-radius: 50%;
  margin: 0 auto 1rem;
  object-fit: cover;
}

.memberName {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: #1f2937;
}

.memberPosition {
  color: #2563eb;
  font-weight: 500;
  margin-bottom: 0.75rem;
}

.memberDescription {
  color: #4b5563;
  font-size: 0.875rem;
  line-height: 1.5;
}

.statistics {
  padding: 4rem 0;
  background: #2563eb;
  color: white;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  text-align: center;
}

.statNumber {
  font-size: 2.25rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.statLabel {
  color: #bfdbfe;
  font-size: 0.875rem;
}

.contactCta {
  padding: 4rem 0;
}

.ctaContainer {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 3rem;
  max-width: 32rem;
  margin: 0 auto;
  text-align: center;
}

.ctaTitle {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #1f2937;
}

.ctaDescription {
  color: #4b5563;
  margin-bottom: 2rem;
  line-height: 1.625;
}

.ctaButtons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.ctaButton {
  padding: 0.75rem 2rem;
  border-radius: 0.5rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
}

.ctaPrimary {
  background: #2563eb;
  color: white;
}

.ctaPrimary:hover {
  background: #1d4ed8;
}

.ctaSecondary {
  border: 1px solid #2563eb;
  color: #2563eb;
  background: white;
}

.ctaSecondary:hover {
  background: #2563eb;
  color: white;
}

/* Responsive design */
@media (max-width: 768px) {
  .headerContent {
    padding: 1.5rem 1rem;
  }
  
  .heroSection {
    padding: 3rem 0;
  }
  
  .heroTitle {
    font-size: 1.875rem;
  }
  
  .heroDescription {
    font-size: 1.125rem;
  }
  
  .missionVision,
  .coreValues,
  .timeline,
  .team,
  .statistics,
  .contactCta {
    padding: 2.5rem 0;
  }
  
  .mvGrid {
    grid-template-columns: 1fr;
  }
  
  .valuesGrid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .teamGrid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .statsGrid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .ctaButtons {
    flex-direction: column;
    align-items: center;
  }
  
  .ctaButton {
    width: 200px;
  }
}

@media (max-width: 480px) {
  .valuesGrid,
  .teamGrid,
  .statsGrid {
    grid-template-columns: 1fr;
  }
  
  .timelineItem {
    flex-direction: column;
    text-align: center;
  }
  
  .timelineYear {
    margin-right: 0;
    margin-bottom: 1rem;
  }
}
