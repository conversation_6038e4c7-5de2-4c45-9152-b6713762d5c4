/* OnlineServices page specific styles */
.onlineServicesPage {
  background-color: #f9fafb;
  min-height: 100vh;
}

.pageHeader {
  background: white;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.headerContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.pageTitle {
  font-size: 1.875rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.pageSubtitle {
  color: #4b5563;
}

.mainContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
}

.servicesArea {
  /* Services area styles */
}

.serviceDetails {
  /* Service details styles */
}

.servicesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.serviceCard {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.serviceCard:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.serviceCard.selected {
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.cardHeader {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.serviceIcon {
  font-size: 1.875rem;
  flex-shrink: 0;
}

.cardInfo {
  flex: 1;
}

.cardTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.statusBadge {
  padding: 0.125rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
}

.statusAvailable {
  background: #dcfce7;
  color: #059669;
}

.statusMaintenance {
  background: #fef3c7;
  color: #d97706;
}

.cardDescription {
  color: #4b5563;
  font-size: 0.875rem;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.actionButton {
  width: 100%;
  padding: 0.75rem;
  border-radius: 0.5rem;
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.actionAvailable {
  background: #2563eb;
  color: white;
}

.actionAvailable:hover {
  background: #1d4ed8;
}

.actionDisabled {
  background: #d1d5db;
  color: #6b7280;
  cursor: not-allowed;
}

.detailsCard {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  position: sticky;
  top: 1rem;
}

.detailsHeader {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.detailsTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
}

.detailsDescription {
  color: #4b5563;
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.stepsTitle {
  font-weight: 600;
  margin-bottom: 1rem;
  color: #1f2937;
}

.stepsList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.stepItem {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.stepNumber {
  background: #2563eb;
  color: white;
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  font-weight: 600;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.stepText {
  color: #374151;
  line-height: 1.5;
}

.startButton {
  width: 100%;
  background: #2563eb;
  color: white;
  padding: 0.75rem;
  border-radius: 0.5rem;
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.startButton:hover {
  background: #1d4ed8;
}

.maintenanceAlert {
  background: #fef3c7;
  border: 1px solid #fbbf24;
  border-radius: 0.5rem;
  padding: 1rem;
}

.alertHeader {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.alertTitle {
  font-weight: 600;
  color: #92400e;
}

.alertDescription {
  color: #a16207;
  font-size: 0.875rem;
  line-height: 1.5;
}

.emptyState {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 3rem 1.5rem;
  text-align: center;
}

.emptyIcon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.emptyTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.emptyDescription {
  color: #4b5563;
}

.helpSection {
  margin-top: 3rem;
  background: #eff6ff;
  border-radius: 0.5rem;
  padding: 2rem;
  text-align: center;
}

.helpTitle {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #1f2937;
}

.helpDescription {
  color: #4b5563;
  margin-bottom: 1.5rem;
}

.helpButtons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.helpButton {
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.helpPrimary {
  background: #059669;
  color: white;
}

.helpPrimary:hover {
  background: #047857;
}

.helpSecondary {
  background: #2563eb;
  color: white;
}

.helpSecondary:hover {
  background: #1d4ed8;
}

.helpTertiary {
  border: 1px solid #d1d5db;
  color: #4b5563;
  background: white;
}

.helpTertiary:hover {
  background: #f9fafb;
}

/* Responsive design */
@media (max-width: 1024px) {
  .mainContent {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .detailsCard {
    position: static;
  }
}

@media (max-width: 768px) {
  .headerContent {
    padding: 1.5rem 1rem;
  }
  
  .mainContent {
    padding: 1.5rem 1rem;
  }
  
  .servicesGrid {
    grid-template-columns: 1fr;
  }
  
  .helpButtons {
    flex-direction: column;
    align-items: center;
  }
  
  .helpButton {
    width: 200px;
    justify-content: center;
  }
}
